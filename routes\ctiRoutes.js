// routes/ctiRoutes.js
const express = require('express');
const router = express.Router();
const {
  saveCTIResults,
  loadCTIResults
} = require('../controllers/threatIntelligenceController');

// Note: These routes are intentionally not protected to allow easier testing
// In production, you may want to add authentication middleware

// @route   POST /api/cti/save-results
// @desc    Save CTI analysis results
// @access  Public (for now)
router.post('/save-results', saveCTIResults);

// @route   GET /api/cti/load-results
// @desc    Load CTI analysis results
// @access  Public (for now)
router.get('/load-results', loadCTIResults);

module.exports = router;
