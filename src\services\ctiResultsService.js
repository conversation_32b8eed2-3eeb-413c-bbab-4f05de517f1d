// src/services/ctiResultsService.js
import { api } from '../api/apiClient';

/**
 * Service for handling CTI results operations
 */
const ctiResultsService = {
  /**
   * Get CTI results for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} CTI results data
   */
  getCTIResults: async (analysisId) => {
    try {
      console.log('Fetching CTI results for analysis:', analysisId);
      const response = await api.get(`/analyses/${analysisId}/cti-results`);
      console.log('CTI fetch response:', response);
      return response;
    } catch (error) {
      // If it's a 404, this is expected when no CTI data exists - handle silently
      if (error.response?.status === 404) {
        console.log('No CTI results found for analysis:', analysisId, '(this is normal if no analysis has been performed yet)');
        return { data: null };
      }
      // Only log actual errors (not 404s)
      console.error('Error fetching CTI results:', error);
      throw error;
    }
  },

  /**
   * Save CTI results for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Object} ctiData - CTI results data to save
   * @returns {Promise<Object>} Response data
   */
  saveCTIResults: async (analysisId, ctiData) => {
    try {
      console.log('Saving CTI results for analysis:', analysisId);
      console.log('CTI data to save:', {
        attackPathsCount: ctiData.attackPaths?.length || 0,
        vulnerabilitiesCount: ctiData.vulnerabilities?.length || 0,
        techniquesCount: ctiData.techniques?.length || 0
      });

      const requestBody = {
        data: {
          ...ctiData,
          savedAt: new Date().toISOString(),
          version: ctiData.version || 1
        }
      };

      const response = await api.post(`/analyses/${analysisId}/cti-results`, requestBody);
      console.log('CTI results saved successfully');
      console.log('Save response:', response);
      return response;
    } catch (error) {
      console.error('Error saving CTI results:', error);
      throw error;
    }
  },

  /**
   * Update CTI results for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Object} ctiData - CTI results data to update
   * @returns {Promise<Object>} Response data
   */
  updateCTIResults: async (analysisId, ctiData) => {
    try {
      console.log('Updating CTI results for analysis:', analysisId);

      const requestBody = {
        data: {
          ...ctiData,
          updatedAt: new Date().toISOString(),
          version: (ctiData.version || 1) + 1
        }
      };

      const response = await api.put(`/analyses/${analysisId}/cti-results`, requestBody);
      console.log('CTI results updated successfully');
      return response;
    } catch (error) {
      console.error('Error updating CTI results:', error);
      throw error;
    }
  },

  /**
   * Delete CTI results for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Response data
   */
  deleteCTIResults: async (analysisId) => {
    try {
      console.log('Deleting CTI results for analysis:', analysisId);
      const response = await api.delete(`/analyses/${analysisId}/cti-results`);
      console.log('CTI results deleted successfully');
      return response;
    } catch (error) {
      // If it's a 404, consider it already deleted (handle gracefully)
      if (error.response?.status === 404) {
        console.log('CTI results not found for deletion (already deleted or never existed)');
        return { success: true, message: 'CTI results already deleted' };
      }
      // Only log actual errors (not 404s)
      console.error('Error deleting CTI results:', error);
      throw error;
    }
  },

  /**
   * Check if CTI results exist for an analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<boolean>} True if CTI data exists
   */
  hasCTIResults: async (analysisId) => {
    try {
      const response = await ctiResultsService.getCTIResults(analysisId);
      return response && response.data && (
        response.data.attackPaths?.length > 0 ||
        response.data.vulnerabilities?.length > 0 ||
        response.data.techniques?.length > 0
      );
    } catch (error) {
      return false;
    }
  },

  /**
   * Format CTI data for saving
   * @param {Array} attackPaths - Selected attack paths
   * @param {Array} vulnerabilities - Vulnerability results
   * @param {Array} techniques - Attack technique results
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Formatted CTI data
   */
  formatCTIData: (attackPaths, vulnerabilities, techniques, metadata = {}) => {
    return {
      attackPaths: attackPaths || [],
      vulnerabilities: vulnerabilities || [],
      techniques: techniques || [],
      metadata: {
        ...metadata,
        totalAttackPaths: attackPaths?.length || 0,
        totalVulnerabilities: vulnerabilities?.length || 0,
        totalTechniques: techniques?.length || 0,
        analysisDate: new Date().toISOString()
      },
      summary: {
        criticalVulnerabilities: vulnerabilities?.filter(v => v.severity === 'CRITICAL')?.length || 0,
        highVulnerabilities: vulnerabilities?.filter(v => v.severity === 'HIGH')?.length || 0,
        mediumVulnerabilities: vulnerabilities?.filter(v => v.severity === 'MEDIUM')?.length || 0,
        lowVulnerabilities: vulnerabilities?.filter(v => v.severity === 'LOW')?.length || 0,
        mitreAttackTechniques: techniques?.filter(t => t.framework === 'MITRE ATT&CK')?.length || 0,
        mitreAtlasTechniques: techniques?.filter(t => t.framework === 'MITRE ATLAS')?.length || 0
      }
    };
  },

  /**
   * Extract business assets from attack paths
   * @param {Array} attackPaths - Attack paths array
   * @returns {Array} Unique business assets
   */
  extractBusinessAssets: (attackPaths) => {
    const assets = new Set();

    attackPaths?.forEach(path => {
      if (path.businessValueName) {
        assets.add(path.businessValueName);
      }
    });

    return Array.from(assets);
  },

  /**
   * Group CTI results by attack path
   * @param {Object} ctiData - CTI results data
   * @returns {Object} Grouped results by attack path
   */
  groupResultsByAttackPath: (ctiData) => {
    const grouped = {};

    ctiData.attackPaths?.forEach(path => {
      grouped[path.id] = {
        attackPath: path,
        vulnerabilities: ctiData.vulnerabilities?.filter(v =>
          v.attackPathId === path.id
        ) || [],
        techniques: ctiData.techniques?.filter(t =>
          t.attackPathId === path.id
        ) || []
      };
    });

    return grouped;
  }
};

export default ctiResultsService;
