// test-enhanced-scenarios.js
// Test script to verify enhanced operational scenarios generation

const axios = require('axios');

// Test data with CTI information
const testData = {
  analysisId: "test-analysis-123",
  attackPaths: [
    {
      id: "test-path-1",
      referenceCode: "CA01",
      sourceRiskName: "Groupe cybercriminel organisé",
      objectifVise: "Vol de données clients",
      dreadedEventName: "Compromission base de données clients",
      businessValueName: "Données clients sensibles",
      businessValueId: "bv-001",
      dreadedEventId: "de-001",
      description: "Chemin d'attaque ciblant la base de données clients via vulnérabilités web",
      difficulty: "Moyen",
      impact: "Élevé",
      stakeholders: [
        {
          name: "Fournisseur hébergement web",
          type: "Fournisseur",
          cyberMaturity: "Moyen",
          trust: "Élevé"
        }
      ],
      vulnerabilities: [
        {
          cveId: "CVE-2023-12345",
          severity: "HIGH",
          score: 8.5,
          description: "Vulnérabilité d'injection SQL dans l'application web permettant l'accès non autorisé à la base de données",
          publishedDate: "2023-01-15",
          configurations: ["Apache Tomcat 9.0.x", "MySQL 8.0.x"],
          weaknesses: [
            { id: "CWE-89", description: "SQL Injection" }
          ]
        }
      ],
      attackTechniques: [
        {
          id: "T1190",
          name: "Exploit Public-Facing Application",
          description: "Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program using software, data, or commands in order to cause unintended or unanticipated behavior.",
          platforms: ["Linux", "Windows", "macOS"],
          tactics: ["Initial Access"],
          kill_chain_phases: ["exploitation"],
          x_mitre_detection: "Monitor application logs for unusual database queries and failed authentication attempts",
          x_mitre_mitigation: "Regular security updates and input validation"
        },
        {
          id: "T1005",
          name: "Data from Local System",
          description: "Adversaries may search local system sources, such as file systems or local databases, to find files of interest and sensitive data prior to Exfiltration.",
          platforms: ["Linux", "Windows", "macOS"],
          tactics: ["Collection"],
          kill_chain_phases: ["collection"],
          x_mitre_detection: "Monitor file access patterns and database query logs",
          x_mitre_mitigation: "Implement access controls and data loss prevention"
        }
      ]
    }
  ],
  existingScenarios: []
};

async function testEnhancedScenarios() {
  try {
    console.log('🧪 Testing Enhanced Operational Scenarios Generation...\n');
    
    const response = await axios.post('http://localhost:5000/api/ai/generate-operational-scenarios', testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60 seconds timeout
    });

    if (response.data.success) {
      console.log('✅ Scenarios generated successfully!');
      console.log(`📊 CTI Data Fidelity Score: ${response.data.data.validation.ctiDataFidelity}%`);
      console.log(`🔍 CVE Data Used: ${response.data.data.validation.ctiDataUsed.vulnerabilities}`);
      console.log(`🎯 MITRE Techniques Used: ${response.data.data.validation.ctiDataUsed.techniques}`);
      
      if (response.data.data.validation.warnings.length > 0) {
        console.log('\n⚠️ Validation Warnings:');
        response.data.data.validation.warnings.forEach(warning => {
          console.log(`  - ${warning}`);
        });
      } else {
        console.log('\n✅ No validation warnings - All CTI data properly used!');
      }

      console.log('\n📋 Generated Scenarios:');
      response.data.data.scenarios.forEach((scenario, index) => {
        console.log(`\n${index + 1}. ${scenario.name}`);
        console.log(`   Description: ${scenario.description.substring(0, 100)}...`);
        console.log(`   Severity: ${scenario.severity}, Likelihood: ${scenario.likelihood}`);
        console.log(`   Steps: ${scenario.steps.length} phases`);
        
        // Check if CVE is mentioned
        const cveMatches = scenario.description.match(/CVE-\d{4}-\d{4,}/g) || [];
        if (cveMatches.length > 0) {
          console.log(`   ✅ CVE References: ${cveMatches.join(', ')}`);
        }
        
        // Check if MITRE techniques are mentioned
        const mitreMatches = scenario.description.match(/T\d{4}(?:\.\d{3})?/g) || [];
        if (mitreMatches.length > 0) {
          console.log(`   ✅ MITRE Techniques: ${mitreMatches.join(', ')}`);
        }
      });

    } else {
      console.error('❌ Failed to generate scenarios:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testEnhancedScenarios();
}

module.exports = { testEnhancedScenarios };
